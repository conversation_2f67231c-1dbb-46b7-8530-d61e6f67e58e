import React from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { AxiosError } from 'axios';
import {
  FormItem,
  Form,
  Input,
  IconCard,
  Typography,
  Card,
  PhoneInputWithCountry
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import TagsInput from '@/shared/components/common/TagsInput/TagsInput';
import { useFormErrors } from '@/shared/hooks/form';
import { createCustomerFormSchema, CustomerFormValues } from '../../schemas/customer.schema';
import { useCreateConvertCustomer } from '../../hooks/useCustomerQuery';
import { CreateConvertCustomerDto } from '../../services/customer.service';

interface CustomerFormProps {
  /**
   * Dữ liệu ban đầu cho form (dùng cho chỉnh sửa)
   */
  initialData?: Partial<CustomerFormValues>;

  /**
   * Callback khi submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi hủy form
   */
  onCancel: () => void;

  /**
   * Tiêu đề form
   */
  title?: string;

  /**
   * Form ref từ parent component để xử lý lỗi
   */
  formRef?: React.RefObject<FormRef<FieldValues>>;
}

/**
 * Component form thêm/chỉnh sửa khách hàng
 */
const CustomerForm: React.FC<CustomerFormProps> = ({
  initialData,
  onSuccess,
  onCancel,
  title,
  formRef: externalFormRef,
}) => {
  const { t } = useTranslation(['business', 'validation', 'common']);
  // Hook để xử lý lỗi form
  const { formRef: formRefFromHook, setFormErrors } = useFormErrors<CustomerFormValues>();

  // Sử dụng external formRef nếu có, nếu không thì dùng formRef từ hook
  const formRef = (externalFormRef || formRefFromHook) as React.RefObject<FormRef<FieldValues>>;

  // Hook để tạo khách hàng mới
  const createCustomerMutation = useCreateConvertCustomer({
    showSuccessNotification: true,
    showErrorNotification: false, // Để component xử lý lỗi cụ thể
  });

  // Tạo schema với translation
  const customerFormSchema = createCustomerFormSchema(t);

  // Giá trị mặc định cho form
  const defaultValues: CustomerFormValues = {
    name: '',
    email: '',
    phone: '',
    tags: '',
    ...initialData,
  };

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    const formValues = values as CustomerFormValues;

    // Chuyển đổi dữ liệu form sang format API
    const customerData: CreateConvertCustomerDto = {
      name: formValues.name,
      phone: formValues.phone,
      ...(formValues.email && formValues.email.trim() && {
        email: {
          primary: formValues.email,
        }
      }),
      tags: formValues.tags
        ? formValues.tags
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag)
        : [],
    };

    // Sử dụng mutate để có thể handle error trong onError callback
    createCustomerMutation.mutate(customerData, {
      onSuccess: () => {
        // Gọi callback success từ parent
        onSuccess?.();
      },
      onError: (error: AxiosError) => {
        console.error('Error creating customer:', error);
        console.log('🔍 Error response data:', error.response?.data);
        console.log('🔍 Error response status:', error.response?.status);

        // Xử lý lỗi API cụ thể
        if (error.response?.data) {
          const errorData = error.response.data as {
            code?: number;
            message?: string;
            errors?: Record<string, string>;
            detail?: {
              details?: Array<{
                field?: string;
                messages?: string[];
              }>;
            };
          };

          console.log('🔍 Parsed error data:', {
            code: errorData.code,
            message: errorData.message,
            hasDetail: !!errorData.detail,
            detailsLength: errorData.detail?.details?.length || 0,
            details: errorData.detail?.details
          });

          // Xử lý lỗi số điện thoại đã tồn tại (code 30187)
          if (errorData.code === 30187) {
            setFormErrors({
              phone: errorData.message || 'Số điện thoại đã tồn tại trong hệ thống',
            });
            return;
          }

          // Xử lý lỗi số điện thoại không hợp lệ (code 9001)
          if (errorData.code === 9001) {
            console.log('🔍 Processing error code 9001');
            // Tìm lỗi phone trong detail.details
            const phoneError = errorData.detail?.details?.find(detail => detail.field === 'phone');
            const phoneErrorMessage = phoneError?.messages?.[0] || 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ';

            console.log('🔍 Phone error found:', {
              phoneError,
              phoneErrorMessage,
              formRef: !!formRef.current
            });

            setFormErrors({
              phone: phoneErrorMessage,
            });

            console.log('🔍 setFormErrors called with:', { phone: phoneErrorMessage });
            return;
          }

          // Xử lý lỗi validation từ API (nếu có)
          if (errorData.errors) {
            setFormErrors(errorData.errors);
            return;
          }

          // Xử lý các lỗi khác
          if (errorData.message) {
            setFormErrors({
              general: errorData.message,
            });
          }
        }
      },
    });
  };

  return (
    <Card className="space-y-6">
      {/* Header */}
      <div>
        <Typography variant="h4" className="text-foreground">
          {title || t('business:customer.addForm')}
        </Typography>
      </div>

      {/* Form */}
      <Form
        ref={formRef}
        schema={customerFormSchema}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        <FormItem name="name" label={t('business:common.form.name')} required>
          <Input
            placeholder={t('business:customer.form.namePlaceholder')}
            fullWidth
            disabled={createCustomerMutation.isPending}
          />
        </FormItem>

        {/* Email */}
        <FormItem name="email" label={t('business:common.form.email')}>
          <Input
            type="email"
            placeholder={t('business:customer.form.emailPlaceholder')}
            fullWidth
            disabled={createCustomerMutation.isPending}
          />
        </FormItem>

        {/* Số điện thoại */}
        <FormItem name="phone" label={t('business:common.form.phone')} required>
          <PhoneInputWithCountry
            placeholder={t('business:customer.form.phonePlaceholder')}
            fullWidth
            disabled={createCustomerMutation.isPending}
            defaultCountry="VN"
          />
        </FormItem>

        {/* Tag khách hàng */}
        <FormItem name="tags" label={t('business:common.form.tags')}>
          <TagsInput
            fieldName="tags"
            placeholder={t('business:customer.form.tagsPlaceholder')}
            formRef={formRef}
            initialValue={initialData?.tags}
            readOnly={createCustomerMutation.isPending}
          />
        </FormItem>

        {/* Action buttons */}
        <div className="flex justify-end space-x-3">
          {/* Debug button - remove in production */}
          <IconCard
            icon="bug"
            variant="secondary"
            size="md"
            title="Test Error"
            onClick={() => {
              console.log('🔍 Testing setFormErrors...');
              setFormErrors({
                phone: 'Test error message for phone field',
              });
            }}
            disabled={createCustomerMutation.isPending}
          />
          <IconCard
            icon="x"
            variant="default"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={createCustomerMutation.isPending}
          />
          <IconCard
            icon="check"
            variant="primary"
            size="md"
            title={t('common:save')}
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={createCustomerMutation.isPending}
            isLoading={createCustomerMutation.isPending}
          />
        </div>
      </Form>
    </Card>
  );
};

export default CustomerForm;
export type { CustomerFormValues };
