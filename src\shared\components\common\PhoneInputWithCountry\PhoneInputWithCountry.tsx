import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { Input, CountrySelect } from '@/shared/components/common';
import { Country, findCountryByCode } from '@/shared/data/countries';

export interface PhoneInputWithCountryProps {
  /**
   * Giá trị số điện thoại (có thể là số điện thoại thường hoặc số quốc tế)
   */
  value?: string;

  /**
   * Callback khi giá trị thay đổi - trả về số điện thoại quốc tế đầy đủ (ví dụ: +84987654321)
   */
  onChange?: (internationalPhone: string) => void;

  /**
   * Placeholder cho input số điện thoại
   */
  placeholder?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Name attribute
   */
  name?: string;

  /**
   * ID attribute
   */
  id?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Error message
   */
  error?: string;

  /**
   * Helper text
   */
  helperText?: string;

  /**
   * Size
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Full width
   */
  fullWidth?: boolean;

  /**
   * Quốc gia mặc định (country code)
   */
  defaultCountry?: string;

  /**
   * Auto complete
   */
  autoComplete?: string;
}

/**
 * Component PhoneInputWithCountry - Input số điện thoại với chọn quốc gia
 */
const PhoneInputWithCountry = forwardRef<HTMLInputElement, PhoneInputWithCountryProps>(
  (
    {
      value = '',
      onChange,
      placeholder = '',
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      defaultCountry = 'VN',
      autoComplete = 'off',
    },
    ref
  ) => {
    // State cho country selection
    const [selectedCountry, setSelectedCountry] = useState<string>(() => {
      // Lấy country từ localStorage hoặc default
      return localStorage.getItem('country') || defaultCountry;
    });

    const inputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    useImperativeHandle(ref, () => inputRef.current as HTMLInputElement);

    // Parse số điện thoại để tách country code và phone number
    const parsePhoneNumber = (phoneValue: string) => {
      if (!phoneValue) {
        return { countryCode: selectedCountry, phoneNumber: '' };
      }

      // Nếu đã có dấu +, parse để tách country code
      if (phoneValue.startsWith('+')) {
        // Tìm country code phù hợp
        if (phoneValue.startsWith('+84')) {
          return { countryCode: 'VN', phoneNumber: phoneValue.substring(3) };
        }
        if (phoneValue.startsWith('+1')) {
          return { countryCode: 'US', phoneNumber: phoneValue.substring(2) };
        }
        // Thêm các country code khác nếu cần

        // Nếu không tìm thấy, giữ nguyên
        return { countryCode: selectedCountry, phoneNumber: phoneValue.replace(/^\+/, '') };
      }

      // Nếu không có dấu +, coi như là số điện thoại local
      return { countryCode: selectedCountry, phoneNumber: phoneValue };
    };

    // State cho phone number (không bao gồm country code)
    const [phoneNumber, setPhoneNumber] = useState<string>(() => {
      const { phoneNumber: parsed } = parsePhoneNumber(value);
      return parsed;
    });

    // Cập nhật phone number khi value từ bên ngoài thay đổi
    useEffect(() => {
      const { countryCode, phoneNumber: parsed } = parsePhoneNumber(value);
      setPhoneNumber(parsed);
      if (countryCode !== selectedCountry) {
        setSelectedCountry(countryCode);
      }
    }, [value]);

    // Handler cho country change
    const handleCountryChange = (country: Country) => {
      setSelectedCountry(country.code);
      // Lưu country vào localStorage
      localStorage.setItem('country', country.code);

      // Tạo số điện thoại quốc tế mới với country code mới
      if (onChange && phoneNumber) {
        const selectedCountryData = findCountryByCode(country.code);
        const dialCode = selectedCountryData?.dialCode || '84';
        const internationalPhone = `+${dialCode}${phoneNumber}`;
        onChange(internationalPhone);
      }
    };

    // Handler cho phone input change
    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newPhoneNumber = e.target.value;
      setPhoneNumber(newPhoneNumber);

      if (onChange) {
        if (newPhoneNumber) {
          const selectedCountryData = findCountryByCode(selectedCountry);
          const dialCode = selectedCountryData?.dialCode || '84';
          const internationalPhone = `+${dialCode}${newPhoneNumber}`;
          onChange(internationalPhone);
        } else {
          onChange('');
        }
      }
    };

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        <div className="flex">
          {/* Country Select - không có border radius bên phải */}
          <div className="flex-shrink-0">
            <CountrySelect
              value={selectedCountry}
              onChange={handleCountryChange}
              compact={true}
              size={size}
              disabled={disabled}
              className="[&>div]:rounded-r-none [&>div]:border-r-0"
            />
          </div>

          {/* Phone Input - không có border radius bên trái */}
          <div className="flex-1">
            <Input
              ref={inputRef}
              type="text"
              value={phoneNumber}
              onChange={handlePhoneChange}
              placeholder={placeholder}
              disabled={disabled}
              name={name}
              id={id}
              fullWidth
              autoComplete={autoComplete}
              error={error}
              className="[&>div>input]:rounded-l-none [&>div>input]:border-l-0 [&>div>input]:focus:border-l-0"
            />
          </div>
        </div>

        {/* Error message */}
        {error && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-muted-foreground">{helperText}</p>
        )}
      </div>
    );
  }
);

PhoneInputWithCountry.displayName = 'PhoneInputWithCountry';

export default PhoneInputWithCountry;
